import { notFound } from 'next/navigation'
import { Locale } from '@/config/global'
import type { Metadata } from 'next'
import { genPageMetadata } from '@/lib/seo'
import { useTranslations } from 'next-intl'
import { Video, Download, Zap } from 'lucide-react'

// 页面元数据配置
const METADATA: Record<Locale, { title: string; description: string }> = {
  en: {
    title: 'SnapAny Video Download Extension',
    description: 'Install the extension to quickly download videos from any site. Easy to use, safe, and completely free—forever.'
  },
  zh: {
    title: 'SnapAny 视频下载插件',
    description: '安装 SnapAny 插件，轻松保存网页视频。操作便捷，安全可靠，永久免费使用。'
  },
  ja: {
    title: 'SnapAny ビデオダウンロード拡張機能',
    description: '拡張機能をインストールして、あらゆるサイトから動画を素早くダウンロード。使いやすく、安全で、完全に無料—永続的に。'
  },
  es: {
    title: 'SnapAny Extensión de Descarga de Videos',
    description: 'Instala la extensión para descargar rápidamente videos de cualquier sitio. Fácil de usar, seguro y completamente gratis—para siempre.'
  }
}

export async function generateMetadata({ params }: { params: { locale: Locale } }): Promise<Metadata> {
  const metadata = METADATA[params.locale]
  if (!metadata) {
    notFound()
  }

  return genPageMetadata({
    title: metadata.title,
    description: metadata.description,
    pathname: '/downloader',
    locale: params.locale,
  })
}

export default function DownloaderPage({ params }: { params: { locale: Locale } }) {
  const t = useTranslations('downloader')

  return (
    <main className="mb-8">
      {/* Hero Section with existing background */}
      <section className="bg-white dark:bg-gray-900 bg-[url('/images/hero-pattern.svg')] dark:bg-[url('/images/hero-pattern-dark.svg')]">
        <div className="bg-gradient-to-b from-blue-50 to-transparent dark:from-blue-900 w-full h-full">
          <div className="container flex flex-col items-center py-12 sm:py-16 gap-4">
            <div className="flex flex-col gap-4 items-center text-center">
              <h1 className="text-gray-900 tracking-tight text-3xl sm:text-4xl lg:text-5xl font-bold">
                {t('title')}
              </h1>
              <h2 className="text-gray-500 text-lg lg:text-xl">
                {t('subtitle')}
              </h2>
            </div>

            {/* Placeholder for downloader component */}
            <div className="w-full max-w-4xl mt-8">
              <div className="flex flex-col justify-center items-center p-6 gap-4 relative w-full h-80 bg-white shadow-lg rounded-lg">
                <div className="flex flex-col items-center gap-4">
                  <div className="text-gray-400 text-lg">
                    {t('placeholder')}
                  </div>
                  <div className="text-gray-500 text-sm text-center max-w-md">
                    {t('placeholderDesc')}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="self-stretch inline-flex justify-start items-start gap-8">
            <div className="flex-1 inline-flex flex-col justify-start items-center gap-6">
              <div className="w-10 h-10 rounded-md inline-flex justify-center items-center">
                <div className="w-10 h-10 relative overflow-hidden">
                  <Video className="w-8 h-8 text-primary-700" fill="currentColor" />
                </div>
              </div>
              <div className="self-stretch flex flex-col justify-start items-start gap-2">
                <div className="self-stretch text-center justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-normal">{t('feature1Title')}</div>
                <div className="self-stretch text-center justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('feature1Desc')}</div>
              </div>
            </div>
            <div className="flex-1 inline-flex flex-col justify-start items-center gap-6">
              <div className="w-10 h-10 rounded-md inline-flex justify-center items-center">
                <div className="w-10 h-10 relative overflow-hidden">
                  <Download className="w-8 h-8 text-primary-700" />
                </div>
              </div>
              <div className="self-stretch flex flex-col justify-start items-start gap-2">
                <div className="self-stretch text-center justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-normal">{t('feature2Title')}</div>
                <div className="self-stretch text-center justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('feature2Desc')}</div>
              </div>
            </div>
            <div className="flex-1 inline-flex flex-col justify-start items-center gap-6">
              <div className="w-10 h-10 rounded-md inline-flex justify-center items-center">
                <div className="w-10 h-10 relative overflow-hidden">
                  <Zap className="w-8 h-8 text-primary-700" fill="currentColor" />
                </div>
              </div>
              <div className="self-stretch flex flex-col justify-start items-start gap-2">
                <div className="self-stretch text-center justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-normal">{t('feature3Title')}</div>
                <div className="self-stretch text-center justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('feature3Desc')}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How to Use Section */}
      <section className="px-4 py-24 bg-gray-50 inline-flex flex-col justify-start items-center overflow-hidden">
        <div className="w-full max-w-[1280px] inline-flex justify-start items-center gap-16">
          <div className="flex-1 inline-flex flex-col justify-start items-start gap-8">
            <div className="self-stretch flex flex-col justify-center items-center gap-4">
              <div className="self-stretch justify-center text-gray-900 text-3xl font-extrabold font-['Inter'] leading-9">{t('howToUseTitle')}</div>
            </div>
            <div className="self-stretch flex flex-col justify-start items-start gap-8">
              <div className="self-stretch inline-flex justify-start items-start gap-4">
                <div className="w-8 h-8 px-2.5 py-0.5 bg-primary-100 rounded-[80px] flex justify-center items-center">
                  <div className="text-center justify-start text-primary-800 text-sm font-bold font-['Inter'] leading-tight">1</div>
                </div>
                <div className="flex-1 inline-flex flex-col justify-start items-start gap-3">
                  <div className="self-stretch justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-loose">{t('step1Title')}</div>
                  <div className="self-stretch justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('step1Desc')}</div>
                </div>
              </div>
              <div className="inline-flex justify-start items-start gap-4">
                <div className="w-8 h-8 px-2.5 py-0.5 bg-primary-100 rounded-[80px] flex justify-center items-center">
                  <div className="text-center justify-start text-primary-800 text-sm font-bold font-['Inter'] leading-tight">2</div>
                </div>
                <div className="w-[564px] inline-flex flex-col justify-start items-start gap-3">
                  <div className="self-stretch justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-loose">{t('step2Title')}</div>
                  <div className="self-stretch justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('step2Desc')}</div>
                </div>
              </div>
              <div className="self-stretch inline-flex justify-start items-start gap-4">
                <div className="w-8 h-8 px-2.5 py-0.5 bg-primary-100 rounded-[80px] flex justify-center items-center">
                  <div className="text-center justify-start text-primary-800 text-sm font-bold font-['Inter'] leading-tight">3</div>
                </div>
                <div className="flex-1 inline-flex flex-col justify-start items-start gap-3">
                  <div className="self-stretch justify-start text-gray-900 text-xl font-bold font-['Inter'] leading-loose">{t('step3Title')}</div>
                  <div className="self-stretch justify-start text-gray-500 text-base font-normal font-['Inter'] leading-normal">{t('step3Desc')}</div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex-1 rounded-lg flex justify-start items-start gap-8">
            <div className="flex-1 rounded-lg inline-flex flex-col justify-start items-start">
              <img className="self-stretch h-[512px] object-cover rounded-lg" src="/images/how-to-use-demo.png" alt="How to use SnapAny demonstration" />
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}